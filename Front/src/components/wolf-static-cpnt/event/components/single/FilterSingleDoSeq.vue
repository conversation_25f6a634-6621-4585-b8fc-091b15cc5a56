<script>
import _ from 'lodash'
import { EVENT_CONSTANTS, FilterModelUtil, generateEventTimeDisplayText, STYLE_CONSTANTS } from 'wolf-static-cpnt/event'
import { FilterConfig as FILTER_CONFIG } from 'wolf-static-cpnt/event/config'
import { validationMixin } from 'wolf-static-cpnt/event/mixins'
import BaseFilter from '../../../filter/Filter.vue'
import FilterEventAction from '../fields/FilterEventAction.vue'
import FilterEventFieldSelect from '../fields/FilterEventFieldSelect.vue'
import FilterTimeInput from '../fields/FilterTimeInput.vue'
import FilterSingleWrapper from '../filter/FilterSingleWrapper.vue'

export default {
  name: 'FilterSingleDoSeq',
  components: {
    BaseFilter,
    FilterSingleWrapper,
    FilterEventFieldSelect,
    FilterEventAction,
    FilterTimeInput,
  },
  mixins: [validationMixin],
  inject: {
    filterContext: 'filterContext',
    filterListGroup: {
      from: 'filterListGroup',
      default: () => null,
    },
    actionCollectionData: {
      from: 'actionCollectionData',
      default: () => null,
    },
    isActionCollectionComponent: {
      from: 'isActionCollectionComponent',
      default: () => false,
    },
    actionCollectionContext: {
      from: 'actionCollectionContext',
      default: () => null,
    },
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    onDelete: {
      type: Function,
      default: () => {},
    },
    onAdd: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      EVENT_ACTION: FILTER_CONFIG.EVENT_ACTION,
      _,
      MAX_FILTER_CONDITIONS: EVENT_CONSTANTS.MAX_FILTER_CONDITIONS,
      MAX_TODAY_DO_EVENTS: EVENT_CONSTANTS.MAX_TODAY_DO_EVENTS,
      SEQUENCE_NUMBER_STYLE: STYLE_CONSTANTS.SEQUENCE_NUMBER_STYLE,
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    mode() {
      return this.context.mode || 'edit'
    },
    dataProvider() {
      return this.context.dataProvider || {}
    },
    // 安全地获取完整的过滤组数据
    fullFilterListGroup() {
      return this.filterListGroup ? this.filterListGroup() : null
    },
    action() {
      return this.value.action
    },
    eventInfo() {
      return this.value.eventInfo || {}
    },
    eventTimeDisplayText() {
      return generateEventTimeDisplayText(this.value, FILTER_CONFIG)
    },
    eventFilterProperty() {
      return this.value.eventFilterProperty || {}
    },
    // 获取依次做过事件列表
    todayDoEvents() {
      return this.value.todayDoEvents || []
    },
    // 检查是否为依次做过模式
    isTodayDoSeq() {
      return this.value.isTodayDoSeq()
    },
    // 检查是否可以添加更多依次做过事件
    canAddMoreTodayDoEvents() {
      return this.value.canAddMoreTodayDoEvents && this.value.canAddMoreTodayDoEvents()
    },
    // 检查是否达到最大数量
    isMaxTodayDoEvents() {
      return this.todayDoEvents.length >= MAX_TODAY_DO_EVENTS
    },

    dataProviderComputed() {
      const pickPd = _.pick(this.dataProvider, 'getPropertyList')
      pickPd.eventId = this?.eventInfo?.id || 0
      return pickPd
    },
    showPushData() {
      return this.context.showPushData || false
    },
    isActionCollection() {
      return this.context.isActionCollection || false
    },
  },
  mounted() {
    // 如果没有 则初始化一个
    if (this.value.todayDoEvents.length === 0) {
      this.handleAddClick()
    }
  },
  methods: {
    /**
     * 处理添加按钮点击事件
     */
    handleAddClick() {
      this.houxuxingwei()
    },

    /**
     * 添加后续行为事件
     */
    houxuxingwei() {
      if (this._isMaxTodayDoEventsReached()) {
        console.warn('已达到最大事件数量限制:', EVENT_CONSTANTS.MAX_TODAY_DO_EVENTS)
        return
      }

      const success = this.value.addTodayDoEvent()
      if (success) {
        this.onChange(this.value)
      }
    },

    /**
     * 删除依次做过事件
     * @param {number} index - 事件索引
     */
    removeTodayDoEvent(index) {
      this.value.removeTodayDoEvent(index)
      this.onChange(this.value)
    },

    /**
     * 处理依次做过事件变更
     * @param {number} index - 事件索引
     * @param {object} eventData - 事件数据
     */
    onTodayDoEventChange(index, eventData) {
      this.value.updateTodayDoEvent(index, eventData)
      this.onChange(this.value)
    },

    /**
     * 获取依次做过事件的模拟value对象
     * @param {number} index - 事件索引
     */
    getTodayDoEventValue(index) {
      const event = this.todayDoEvents[index]
      return {
        eventInfo: event.eventInfo,
        changeProperty: (newData) => {
          this.value.updateTodayDoEvent(index, { eventInfo: newData.eventInfo })
          this.onChange(this.value)
        },
      }
    },

    /**
     * 获取依次做过事件的数据提供者
     * @param {number} index - 事件索引
     */
    getTodayDoEventDataProvider(index) {
      const pickPd = _.pick(this.dataProvider, 'getPropertyList')
      pickPd.eventId = this.todayDoEvents[index]?.eventInfo?.id || 0
      return pickPd
    },

    /**
     * 处理依次做过事件过滤器变更
     * @param {number} index - 事件索引
     */
    onTodayDoEventFilterChange(index) {
      return (filterData) => {
        this.value.updateTodayDoEvent(index, { eventFilterProperty: filterData })
        this.onChange(this.value)
      }
    },

    /**
     * 为依次做过事件添加过滤条件
     * @param {number} index - 事件索引
     */
    onAddTodayDoFilter(index) {
      const filterRef = this.$refs[`todayDoFilterRef_${index}`]
      if (filterRef && filterRef[0] && filterRef[0].addFilterGroup) {
        const filters = filterRef[0].addFilterGroup()
        this.value.updateTodayDoEvent(index, { eventFilterProperty: filters })
        this.onChange(this.value)
      }
    },

    /**
     * 检查是否达到最大今日做过事件数量
     * @private
     */
    _isMaxTodayDoEventsReached() {
      return this.todayDoEvents.length >= EVENT_CONSTANTS.MAX_TODAY_DO_EVENTS
    },

    /**
     * 处理过滤器变更
     * @param {object} data - 过滤器数据
     */
    onChangeFilter(data) {
      this.value.changePropertyValue(data)
      this.onChange(this.value)
    },

    onAddFilter() {
      if (this.$refs.filterRef && this.$refs.filterRef.addFilterGroup) {
        const filters = this.$refs.filterRef.addFilterGroup()
        this.value.changePropertyValue(filters)
        this.onChange(this.value)
      }
    },

    /**
     * 处理主过滤器 radio-group 的 change 事件
     */
    onMainPushDataChange(e) {
      this.handlePushDataChange(undefined, e.target.value)
    },

    /**
     * 处理主过滤器 radio 的 click 事件（用于取消选择）
     */
    onMainRadioClick() {
      // 如果当前已经选中，则取消选择
      if (this.value.pushData === true) {
        this.handlePushDataChange(undefined, false)
      }
    },

    /**
     * 处理依次做过事件 radio-group 的 change 事件
     */
    onTodayDoPushDataChange(index, e) {
      this.handlePushDataChange(index, e.target.value)
    },

    /**
     * 处理依次做过事件 radio 的 click 事件（用于取消选择）
     */
    onTodayDoRadioClick(index) {
      // 如果当前已经选中，则取消选择
      if (this.todayDoEvents[index].pushData === true) {
        this.handlePushDataChange(index, false)
      }
    },

    /**
     * 统一的 pushData 处理方法
     * @param {number|undefined} index - 事件索引，undefined表示主过滤器
     * @param {boolean} pushDataValue - 推送数据值
     */
    handlePushDataChange(index, pushDataValue) {
      const isActionCollection = this.isActionCollectionComponent && this.isActionCollectionComponent()
      const actionCollectionContext = this.actionCollectionContext && this.actionCollectionContext()
      if (index === undefined) {
        // 主过滤器的pushData变更
        if (isActionCollection && actionCollectionContext && pushDataValue) {
          actionCollectionContext.setActionCollectionPushDataExclusive(this.fullFilterListGroup)
          console.log('清空了主过滤器')
          FilterModelUtil.setPushDataExclusive(this.fullFilterListGroup, this.value, pushDataValue)
        }
        else if (isActionCollection && !pushDataValue) {
          // 如果是取消选择，只设置自己为 false
          this.value.changePushData(false)
        }
        else {
          // 默认方法
          FilterModelUtil.setPushDataExclusive(this.fullFilterListGroup, this.value, pushDataValue)
        }

        // 如果主过滤器设置为true，需要将所有todayDoEvents的pushData设置为false
        if (pushDataValue) {
          this.value.todayDoEvents.forEach((event, i) => {
            this.value.updateTodayDoEvent(i, { pushData: false })
          })
        }

        this.onChange(this.value)
      }
      else {
        this.value.updateTodayDoEvent(index, { pushData: pushDataValue })
        // 如果设置为true，需要将其他所有过滤器的pushData设置为false
        if (pushDataValue) {
          // 将主过滤器的pushData设置为false
          this.value.changePushData(false)

          // 将其他依次做过事件的pushData设置为false
          this.value.todayDoEvents.forEach((event, i) => {
            if (i !== index) {
              this.value.updateTodayDoEvent(i, { pushData: false })
            }
          })

          // 将其他过滤器的pushData设置为false
          if (this.fullFilterListGroup) {
            this.fullFilterListGroup.filters.forEach((filterGroup) => {
              if (filterGroup && filterGroup.filters) {
                filterGroup.filters.forEach((filter) => {
                  if (filter !== this.value) {
                    filter.changePushData(false)
                  }
                  if (filter?.todayDoEvents) {
                    filter.todayDoEvents.forEach((event, i) => {
                      filter.updateTodayDoEvent(i, { pushData: false })
                    })
                  }
                })
              }
            })
          }
        }
        this.value.updateTodayDoEvent(index, { pushData: pushDataValue })
        this.onChange(this.value)
      }
    },

  },
}
</script>

<template>
  <li :class="`FilterSingle ${mode}`">
    <!-- 主事件行 -->
    <div
      style="display: flex"
      :style="{
        display: mode !== 'edit' && !value.valid().isValid ? 'none' : 'flex',
      }"
    >
      <!-- 实时行为最开始的时间 -->
      <div :class="`FilterField ${mode} ${validator?.firstAction && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper
          :value="eventTimeDisplayText"
          :use-take-place-width="true"
          style="width:auto"
        >
          <FilterTimeInput :value="value" :on-change="onChange" type="first" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件行为 -->
      <div :class="`FilterEventAction ${mode} ${validator?.action && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="EVENT_ACTION[action]" :use-take-place-width="true" style="width: auto;">
          <FilterEventAction :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 事件选择 -->
      <div :class="`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="eventInfo?.displayName" :use-take-place-width="true" :style="mode === 'edit' ? 'min-width: 160px;' : ''">
          <FilterEventFieldSelect :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>

      <!-- 控制器 -->
      <div v-if="mode === 'edit'" class="Ctroller">
        <a-tooltip v-if="value.validating && hasValidationError" placement="topRight" :title="validationMessage">
          <div style="margin-right: 5px; color: #fa7777;">
            <a-icon type="question-circle" />
          </div>
        </a-tooltip>
        <span
          class="handleAdd"
          @click="onAddFilter"
        >
          <a-icon type="plus-circle" /> <span class="add-text">条件</span>
        </span>
        <span
          class="handleAdd"
          @click="handleAddClick"
        >
          <a-icon type="plus-circle" /> <span class="add-text">后续行为</span>
        </span>
        <a-radio-group v-if="showPushData" :value="value.pushData" @change="onMainPushDataChange">
          <a-radio :value="true" @click="onMainRadioClick">
            <span class="checkboxTitle">推送数据</span>
          </a-radio>
        </a-radio-group>
        <a-icon type="close-circle" @click="onDelete" />
      </div>
    </div>

    <!-- 内部过滤器 -->
    <BaseFilter
      ref="filterRef"
      class="innerFilter"
      :data-provider="dataProviderComputed"
      :value="eventFilterProperty"
      :on-change="onChangeFilter"
      :mode="mode"
      :hide-add="true"
      :hide-init="true"
      add-button-text="添加过滤条件"
      :is-action-collection="isActionCollection"
    />

    <!-- 依次做过事件列表 -->
    <div v-if="isTodayDoSeq" style="display: flex !important; flex-direction: column;">
      <div v-for="(event, index) in todayDoEvents" :key="index">
        <div style="margin-left: 48px; display: flex; align-items: center; ">
          <!-- 下标显示 -->
          <div :style="SEQUENCE_NUMBER_STYLE">
            {{ index + 1 }}
          </div>

          <!-- 事件选择 -->
          <div :class="`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`">
            <FilterSingleWrapper :value="event.eventInfo?.displayName || '请选择事件'" :use-take-place-width="true" :style="mode === 'edit' ? 'min-width: 160px;' : ''">
              <FilterEventFieldSelect
                :value="getTodayDoEventValue(index)"
                :on-change="() => {}"
              />
            </FilterSingleWrapper>
          </div>

          <!-- 控制器 -->
          <div v-if="mode === 'edit'" class="Ctroller">
            <span
              class="handleAdd"
              :style="{
                display:
                  $refs[`todayDoFilterRef_${index}`] && $refs[`todayDoFilterRef_${index}`][0]
                  && $refs[`todayDoFilterRef_${index}`][0].getFilterCount
                  && $refs[`todayDoFilterRef_${index}`][0].getFilterCount() >= MAX_FILTER_CONDITIONS
                    ? 'none'
                    : 'inline-block',
              }"
              @click="onAddTodayDoFilter(index)"
            >
              <a-icon type="plus-circle" /> <span class="add-text">条件</span>
            </span>
            <a-radio-group v-if="showPushData" :value="todayDoEvents[index].pushData" @change="onTodayDoPushDataChange(index, $event)">
              <a-radio :value="true" @click="onTodayDoRadioClick(index)">
                <span class="checkboxTitle">推送数据1</span>
              </a-radio>
            </a-radio-group>
            <!-- 删除按钮 -->
            <div v-if="mode === 'edit'" class="h-16">
              <a-icon type="close-circle" style="cursor: pointer;" @click="removeTodayDoEvent(index)" />
            </div>
          </div>
        </div>

        <!-- 内部过滤器 -->
        <BaseFilter
          :ref="`todayDoFilterRef_${index}`"
          class="innerFilter"
          :data-provider="getTodayDoEventDataProvider(index)"
          :value="event.eventFilterProperty"
          :on-change="onTodayDoEventFilterChange(index)"
          :mode="mode"
          :hide-add="true"
          :hide-init="true"
          add-button-text="添加过滤条件"
          :is-action-collection="isActionCollection"
        />
      </div>
    </div>
  </li>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
